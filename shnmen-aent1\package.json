{"name": "waatch-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "turbo run dev", "build": "turbo run build", "lint": "turbo run lint", "test": "turbo run test", "format": "prettier --write .", "docker:dev": "docker-compose -f docker-compose.dev.yml up -d", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate"}, "workspaces": ["apps/*", "packages/*"], "devDependencies": {"@types/node": "^20.11.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-config-turbo": "^1.13.2", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^9.0.11", "prettier": "^3.2.4", "turbo": "^1.13.2", "typescript": "^5.3.3"}, "dependencies": {"@prisma/client": "^5.7.1", "prisma": "^5.7.1"}}
{"name": "api", "version": "0.1.0", "private": true, "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node build/index.js", "lint": "eslint . --max-warnings=0"}, "dependencies": {"@types/node": "^20.11.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^5.1.1", "express-async-errors": "^3.1.1", "helmet": "^7.0.0", "http-status-codes": "^2.2.0", "prisma": "^5.7.1", "tsx": "^4.7.1", "typescript": "^5.3.3", "zod": "^3.22.4"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.1.2", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0"}}
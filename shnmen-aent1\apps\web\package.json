{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --max-warnings=0", "preview": "vite preview", "test": "vitest", "test:ci": "vitest run", "test:e2e": "cypress run"}, "dependencies": {"@tanstack/react-query": "^4.36.1", "@types/node": "^20.11.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "immer": "^10.0.2", "postcss": "^8.4.31", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.3.5", "typescript": "^5.3.3", "vite": "^5.0.0", "zustand": "^4.4.5"}, "devDependencies": {"@testing-library/react": "^14.1.2", "@types/jest": "^29.5.11", "@vitejs/plugin-react": "^4.2.1", "cypress": "^13.6.4", "eslint-plugin-cypress": "^2.13.3", "jsdom": "^23.0.1", "vitest": "^1.2.2"}}